<!DOCTYPE html><html lang="en"><head><meta http-equiv="origin-trial" content="A7vZI3v+Gz7JfuRolKNM4Aff6zaGuT7X0mf3wtoZTnKv6497cVMnhy03KDqX7kBz/q/iidW7srW31oQbBt4VhgoAAACUeyJvcmlnaW4iOiJodHRwczovL3d3dy5nb29nbGUuY29tOjQ0MyIsImZlYXR1cmUiOiJEaXNhYmxlVGhpcmRQYXJ0eVN0b3JhZ2VQYXJ0aXRpb25pbmczIiwiZXhwaXJ5IjoxNzU3OTgwODAwLCJpc1N1YmRvbWFpbiI6dHJ1ZSwiaXNUaGlyZFBhcnR5Ijp0cnVlfQ==">
  <link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin="">

<link href="https://fonts.googleapis.com/css?family=Offside" rel="stylesheet" type="text/css">
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@500&amp;family=Roboto&amp;display=swap" rel="stylesheet">

<meta name="viewport" content="width=device-width, initial-scale=1">

  <title>Augment Login</title>
  <link rel="icon" href="/static/favicon.ico">

  <style type="text/css">
    :root{--augment-light-blue:#2f92db;--augment-dark-blue:#242e4e;--augment-green:#3d855e;--augment-green-dark:#114e0b;--augment-grey:#736d63;--augment-grey-light:#f5f5f5;--page-background:#fff;--text-white-80:#fffc;--system-fonts:-apple-system,BlinkMacSystemFont,avenir next,avenir,segoe ui,helvetica neue,helvetica,Cantarell,Ubuntu,roboto,noto,arial,sans-serif;--p-2:0.5rem;--p-3:0.75rem;--p-4:1rem;--p-8:2rem;--p-14:3.5rem;--gap-2:0.5rem;--gap-6:1.5rem;--text-sm-size:0.875rem;--text-sm-lh:1.25rem;--text-base-size:1rem;--text-lg-size:1.125rem;--text-3xl-size:1.875rem;--text-5xl-size:3rem}*{box-sizing:border-box;-webkit-font-smoothing:antialiased}body{background:var(--page-background);font-family:var(--system-fonts);font-size:var(--text-base-size);margin:0;padding:0}a{color:var(--augment-green);text-decoration:none}.l-oauth{background:var(--augment-grey-light);box-sizing:border-box;display:grid;font-weight:500;grid-auto-rows:1fr auto;height:100dvh}.l-oauth__center{text-align:center}.l-oauth__center,.l-oauth__left{background:#fff;border-radius:24px;display:flex;flex-direction:column;gap:var(--p-4);margin:var(--p-4);max-width:640px;place-self:center}.l-oauth__left{text-align:left}.l-oauth__content{padding:24px}@media (min-width:700px){.l-oauth__content{padding:48px 108px 62px}}.c-header{align-items:baseline;display:grid;grid-template-columns:auto auto;grid-template-rows:auto auto;justify-content:center;margin-bottom:20px}.c-header__welcome{color:var(--augment-green);font-family:Offside;font-size:var(--text-lg-size);font-weight:400;grid-column:1/3;margin-left:48px;opacity:0;text-align:left}.c-header__title{color:var(--augment-green-dark);font-family:Offside;font-size:var(--text-3xl-size);font-weight:400;margin:0;position:relative;top:-.18rem}.c-header__logo svg{height:48px;width:auto}.c-header .c-augi-logo__smile,.c-header:hover .c-augi-logo__default{opacity:0}.c-header:hover .c-augi-logo__smile,.c-header:hover .c-header__welcome{opacity:1}@media (min-width:700px){.c-header{margin-bottom:34px}.c-header__title{font-size:var(--text-5xl-size);position:relative;top:-.18rem}.c-header__logo svg{height:62px}}.c-footer{align-items:center;background-color:var(--augment-green);color:var(--text-white-80);display:flex;flex-direction:column;font-family:"Inter",var(--system-fonts);font-size:var(--text-sm-size);gap:var(--gap-6);justify-content:space-between;line-height:var(--text-sm-lh);padding:var(--p-2) var(--p-4);text-align:center}.c-footer p{margin:var(--p-4) 0}.c-footer a{color:var(--text-white-80);font-size:inherit;padding:var(--p-3)}.c-footer__links{align-items:center;display:flex;flex-direction:column;gap:var(--gap-2)}@media (min-width:540px){.c-footer,.c-footer__links{flex-direction:row}}.u-text-sm{font-size:var(--text-sm-size);line-height:var(--text-sm-lh)}.gsi-material-button{-webkit-appearance:none;background-color:#f2f2f2;background-image:none;border:none;border-radius:20px;box-sizing:border-box;color:#1f1f1f;cursor:pointer;font-family:Roboto,arial,sans-serif;font-size:1rem;height:40px;letter-spacing:.25px;max-width:400px;min-width:-moz-min-content;min-width:min-content;outline:none;overflow:hidden;padding:0 12px;position:relative;text-align:center;transition:background-color .218s,border-color .218s,box-shadow .218s;-moz-user-select:none;-webkit-user-select:none;-ms-user-select:none;vertical-align:middle;white-space:nowrap;width:auto}.gsi-material-button .gsi-material-button-icon{height:20px;margin-right:12px;min-width:20px;width:20px}.gsi-material-button .gsi-material-button-content-wrapper{align-items:center;display:flex;flex-direction:row;flex-wrap:nowrap;height:100%;justify-content:space-between;position:relative;width:100%}.gsi-material-button .gsi-material-button-contents{flex-grow:1;font-family:Roboto,arial,sans-serif;font-weight:500;overflow:hidden;text-overflow:ellipsis;vertical-align:top}.gsi-material-button .gsi-material-button-state{bottom:0;left:0;opacity:0;position:absolute;right:0;top:0;transition:opacity .218s}.gsi-material-button:disabled{background-color:#ffffff61;cursor:default}.gsi-material-button:disabled .gsi-material-button-state{background-color:#1f1f1f1f}.gsi-material-button:disabled .gsi-material-button-contents,.gsi-material-button:disabled .gsi-material-button-icon{opacity:38%}.gsi-material-button:not(:disabled):active .gsi-material-button-state,.gsi-material-button:not(:disabled):focus .gsi-material-button-state{background-color:#001d35;opacity:12%}.gsi-material-button:not(:disabled):hover{box-shadow:0 1px 2px 0 rgba(60,64,67,.3),0 1px 3px 1px rgba(60,64,67,.15)}.gsi-material-button:not(:disabled):hover .gsi-material-button-state{background-color:#001d35;opacity:8%}.user_warning{font-style:italic;font-weight:700}.sign-link{display:inline-block;text-decoration:none}.signin-container .buttons{opacity:.4;pointer-events:none}.signin-container:has(#terms-of-service-checkbox:checked) .buttons{opacity:1;pointer-events:unset}.signin-container .redirect,.signin-container form{display:flex;flex-direction:column;gap:var(--p-4)}.c-checkbox{cursor:pointer;display:inline-block;position:relative}.c-checkbox input{opacity:0;position:absolute;z-index:-1}.c-checkbox--mark{border-color:var(--augment-green);border-radius:2px;border-style:solid;box-sizing:border-box;display:inline-block;height:20px;margin-right:var(--p-2);vertical-align:bottom;width:20px}.c-checkbox--mark:after{border:solid var(--text-white-80);border-width:0 2px 2px 0;content:"";display:none;height:8px;left:8px;position:absolute;top:4px;transform:rotate(45deg);width:3px}.c-checkbox input:checked~.c-checkbox--mark{background:var(--augment-green)}.c-checkbox input:checked~.c-checkbox--mark:after{display:block}.c-checkbox input:focus~.c-checkbox--mark{border-color:var(--augment-green-dark)}
    /* magic pixel height of footer. TODO: clean up CSS for all templates to avoid this kind of hack */
    .l-oauth {
      height: calc(100dvh - 68px);
    }

    .l-oauth__content {
      padding: 24px;
    }

    #signup-button {
      color: #f2f2f2;
      border-radius: 6px;
      background-color: var(--augment-green);
    }

    #signup-button:disabled {
      color: #1f1f1f;
      background-color: #f2f2f2;
      cursor: not-allowed;
    }

    .bottom-links {
      display: flex;
      justify-content: center;
      font-size: 0.875rem;
    }

    .bottom-links a {
      flex: 1;
    }

    .bottom-links-separator::before {
      content: "|";
      margin: 0 0.5rem;
      color: inherit;
    }
  </style>
  <script type="text/javascript" async="" charset="utf-8" src="https://www.gstatic.com/recaptcha/releases/DBIsSQ0s2djD_akThoRUDeHa/recaptcha__en.js" crossorigin="anonymous" integrity="sha384-1WKYge3dK2/UTDzhSp5yz67wCBmi0z81tX6M3swiQAxIbgepbnchtTZFPr6x5sGi"></script><script>
    function getAction() {
      return 'signup';
    }
  </script>
  
<script async="" src="https://js-232.augmentcode.com/prod/bundle.js" verisoul-project-id="9d08ce27-3d9b-4737-b477-e57c7446255b"></script>
<script>!function(e){if(e.Verisoul)return;const r=[],t={},o=new Proxy(t,{get:(e,o)=>o in t?t[o]:(...e)=>new Promise(((t,n)=>r.push([o,e,t,n]))),set:(e,r,o)=>(t[r]=o,!0)});e.Verisoul=o;const n=()=>{Object.keys(t).length&&r.splice(0).forEach((([e,r,o,n])=>{try{Promise.resolve(t[e](...r)).then(o,n)}catch(e){n(e)}}))},c=document.querySelector("script[verisoul-project-id]"),s=()=>r.splice(0).forEach((([,,,e])=>e(new Error("Failed to load Verisoul SDK"))));if(!c)return void s();c.addEventListener("load",n,{once:!0}),c.addEventListener("error",(()=>{clearInterval(i),s()}),{once:!0});const i=setInterval((()=>{Object.keys(t).length&&(clearInterval(i),n())}),40)}(window);</script>


<script src="https://www.google.com/recaptcha/enterprise.js?render=6LcoVhMrAAAAACg2fvNox_iH00SjOIWoewNh_PX1"></script>



<script>
  function withTimeout(promise, timeoutMs) {
    return new Promise((resolve, reject) => {
      const timer = setTimeout(() => reject(new Error("Execution aborted due to timeout.")), timeoutMs);
      promise.finally(() => clearTimeout(timer)).then(resolve, reject);
    });
  }

  async function onClick(e) {
    e.preventDefault();

    
    const verisoulPromise = withTimeout(window.Verisoul.session(), 10000);
    
    
    const grecaptchaPromise = withTimeout((async () => {
      await new Promise((resolve) => {
        grecaptcha.enterprise.ready(resolve);
      });
      return await grecaptcha.enterprise.execute('6LcoVhMrAAAAACg2fvNox_iH00SjOIWoewNh_PX1', { action: getAction() });
    })(), 5000);
    
    
    const verosintPromise = Promise.resolve('');
    

    const errors = [];

    const [verisoulResponse, grecaptchaResponse, verosintResponse] = await Promise.allSettled([verisoulPromise, grecaptchaPromise, verosintPromise]);
    if (verisoulResponse.status === 'fulfilled') {
      document.getElementById('verisoul-session-id').value = verisoulResponse.value.session_id;
    } else {
      console.log('Failed to get verisoul session', verisoulResponse.reason);
      errors.push(`verisoul: ${verisoulResponse.reason.message}`);
    }
    if (grecaptchaResponse.status === 'fulfilled') {
      document.getElementById('g-recaptcha-response').value = grecaptchaResponse.value;
    } else {
      console.log('Failed to get grecaptcha response', grecaptchaResponse.reason);
      errors.push(`recaptcha: ${grecaptchaResponse.reason.message}`);
    }
    if (verosintResponse.status === 'fulfilled') {
      document.getElementById('verosint-deviceid').value = verosintResponse.value;
    } else {
      console.log('Failed to get verosint device id', verosintResponse.reason);
      errors.push(`verosint: ${verosintResponse.reason.message}`);
    }

    // Upload client-side errors to help distinguish real users from bots
    document.getElementById('client-errors').value = JSON.stringify(errors);
    document.getElementById('action-form').submit();
  }

  
  if (typeof window['grecaptcha'] === 'undefined') {
      grecaptcha = {
        ready: function(cb) {
          const c = '___grecaptcha_cfg';
          window[c] = window[c] || {};
          (window[c]['fns'] = window[c]['fns'] || []).push(cb);
        }
      };
  }
  

  
</script>
  <script>
    function updateSignupButton(checkbox) {
      document.getElementById("signup-button").disabled = !checkbox.checked;
    }
  </script>
</head>

<body>
  <div class="l-oauth">
  <div class="l-oauth__center">
  <div class="l-oauth__content">
    <header class="c-header" style="margin-bottom: 15px">
      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 233 32" height="100%" width="100%">
        <path fill="currentColor" fill-rule="evenodd" d="M211.967 2.78h2.887v23h-2.887v-2.569c-1.047 1.809-2.856 2.982-5.584 2.982-3.902 0-7.297-3.236-7.297-8.883 0-5.615 3.395-8.883 7.297-8.883 2.728 0 4.537 1.174 5.584 2.982V2.78Zm-4.854 8.122c-2.951 0-4.886 2.348-4.886 6.408 0 4.061 1.935 6.409 4.886 6.409 2.569 0 4.98-1.904 4.98-6.409s-2.411-6.408-4.98-6.408Zm12.722 7.297c.127 3.68 2.728 5.456 5.266 5.456s3.934-1.142 4.664-2.823h2.982c-.793 2.95-3.49 5.361-7.646 5.361-5.393 0-8.375-3.87-8.375-8.914 0-5.394 3.616-8.852 8.28-8.852 5.203 0 8.344 4.378 7.963 9.772h-13.134Zm.032-2.475h10.056c-.063-2.411-1.871-4.822-4.917-4.822-2.57 0-4.886 1.396-5.139 4.822Zm-29.986 10.47c3.966 0 8.185-2.697 8.185-8.884 0-6.186-4.219-8.883-8.185-8.883s-8.185 2.697-8.185 8.883c0 6.187 4.219 8.883 8.185 8.883Zm5.108-8.884c0 4.378-2.507 6.345-5.108 6.345-2.601 0-5.108-1.808-5.108-6.345 0-4.663 2.507-6.345 5.108-6.345 2.601 0 5.108 1.809 5.108 6.345Zm-14.277-2.887h-2.919c-.412-2.03-2.189-3.458-4.41-3.458-2.569 0-4.917 2-4.917 6.282 0 4.346 2.379 6.408 4.917 6.408 2.411 0 4.125-1.618 4.537-3.426h2.982c-.571 3.204-3.648 5.964-7.614 5.964-4.885 0-7.963-3.712-7.963-8.914 0-5.076 3.204-8.852 8.249-8.852 4.124 0 6.757 3.11 7.138 5.996ZM155.38 4.43h-2.887v4.283h-2.697v2.316h2.697v10.47c0 3.552.761 4.282 4.029 4.282h2.094V23.37h-1.46c-1.649 0-1.776-.444-1.776-2.22V11.028h3.236V8.713h-3.236V4.43Zm-17.659 6.853c.983-1.618 2.633-2.856 5.52-2.856 4.124 0 5.615 2.665 5.615 6.568V25.78h-2.887v-9.93c0-2.633-.444-4.917-3.743-4.917-2.792 0-4.505 1.935-4.505 5.583v9.264h-2.887V8.713h2.887v2.57Zm-12.613 12.372c-2.538 0-5.139-1.776-5.266-5.456h13.134c.381-5.394-2.76-9.772-7.963-9.772-4.663 0-8.28 3.458-8.28 8.852 0 5.044 2.982 8.914 8.375 8.914 4.156 0 6.853-2.41 7.646-5.361h-2.982c-.73 1.681-2.126 2.823-4.664 2.823Zm4.823-7.93h-10.057c.254-3.427 2.569-4.823 5.139-4.823 3.046 0 4.854 2.411 4.918 4.822ZM93.767 25.78H90.88V8.713h2.887v2.57c.983-1.618 2.57-2.856 5.14-2.856 2.601 0 4.155 1.143 4.917 3.014 1.364-2.094 3.362-3.014 5.742-3.014 3.965 0 5.52 2.665 5.52 6.568V25.78h-2.887v-9.93c0-2.633-.508-4.917-3.648-4.917-2.475 0-4.125 1.935-4.125 5.583v9.264h-2.887v-9.93c0-2.633-.507-4.917-3.648-4.917-2.475 0-4.124 1.935-4.124 5.583v9.264ZM87.829 8.713h-2.887v2.697c-1.047-1.809-2.697-2.983-5.425-2.983-3.776 0-7.234 3.078-7.234 8.534 0 5.489 3.458 8.534 7.234 8.534 2.728 0 4.378-1.142 5.425-2.95v1.618c0 2.316-.476 3.109-1.11 3.775-.762.825-1.936 1.27-3.49 1.27-2.665 0-3.585-1.175-3.966-2.697h-3.078c.54 3.458 3.141 5.234 7.012 5.234 2.538 0 4.663-.825 5.9-2.252.984-1.079 1.619-2.506 1.619-5.996V8.713ZM75.424 16.96c0-3.87 1.999-6.06 4.822-6.06 2.57 0 4.823 1.746 4.823 6.06 0 4.347-2.253 6.092-4.823 6.092-2.823 0-4.822-2.19-4.822-6.092Zm-7.927 6.378c-.983 1.618-2.538 2.855-5.361 2.855-3.966 0-5.457-2.665-5.457-6.567V8.713h2.887V18.77c0 2.634.444 4.918 3.585 4.918 2.728 0 4.346-1.936 4.346-5.584v-9.39h2.887v17.068h-2.887v-2.443Zm-16.246-8.06c-7.01.794-11.548 1.968-11.548 5.997 0 3.14 2.665 4.917 5.87 4.917 3.013 0 4.79-1.015 5.837-2.76.032 1.015.127 1.713.222 2.348h2.919c-.318-1.618-.476-3.585-.445-6.44l.032-3.934c.032-4.79-2.094-7.043-6.789-7.043-3.331 0-6.567 2.062-6.852 5.742h2.982c.127-2.094 1.523-3.395 3.902-3.395 2.125 0 3.87 1.047 3.87 4.156v.413Zm-8.343 5.933c0-2.347 3.33-3.109 8.565-3.648v1.079c0 4.029-2.57 5.266-5.266 5.266-2.062 0-3.3-1.079-3.3-2.697ZM14.316.185c.184-.101.433-.14.722-.14s.54.04.725.147a.593.593 0 0 1 .313.536v.008l-.159 4.22a.494.494 0 0 1-.278.446c-.157.083-.366.113-.601.113s-.444-.03-.601-.113a.494.494 0 0 1-.279-.447c-.04-1.05-.072-1.864-.098-2.441v-.005a47.416 47.416 0 0 0-.04-1.257c-.013-.248-.02-.406-.02-.465V.708c0-.108.025-.213.082-.307a.612.612 0 0 1 .234-.216Zm3.354 0c.184-.101.434-.14.722-.14.29 0 .54.04.725.147a.592.592 0 0 1 .313.536v.008l-.158 4.22a.494.494 0 0 1-.28.446c-.156.083-.365.113-.6.113s-.444-.03-.6-.113a.493.493 0 0 1-.279-.447 332.95 332.95 0 0 0-.099-2.441v-.005a46.976 46.976 0 0 0-.04-1.257c-.012-.248-.02-.406-.02-.465V.708c0-.108.026-.213.083-.307a.611.611 0 0 1 .233-.216Zm2.782 6.572c.2-.18.446-.264.727-.264h6.267c.805 0 1.452.215 1.91.672.46.458.673 1.115.673 1.933v5.332c0 .622.127 1.033.335 1.273.201.231.585.383 1.221.398h.01c.254.019.47.118.634.304a.99.99 0 0 1 .24.68c0 .25-.076.475-.233.67a.81.81 0 0 1-.653.313c-.634.015-1.018.167-1.22.398-.209.241-.334.656-.334 1.297v5.332c0 .541-.094 1.013-.293 1.41-.2.402-.498.703-.89.905h-.001c-.39.198-.86.292-1.399.292h-6.058v.005h-.21c-.284 0-.531-.092-.73-.28a.916.916 0 0 1-.301-.68c0-.251.084-.479.251-.668a.88.88 0 0 1 .686-.294h5.82c.296 0 .484-.075.603-.196.119-.119.198-.32.198-.653v-5.38c0-.5.1-.957.304-1.372.2-.413.474-.742.82-.985a1.97 1.97 0 0 1 .176-.11 1.994 1.994 0 0 1-.176-.11 2.518 2.518 0 0 1-.82-.987 3.082 3.082 0 0 1-.304-1.37V9.264c0-.33-.079-.533-.198-.653-.12-.12-.31-.197-.603-.197h-5.82a.886.886 0 0 1-.685-.294.976.976 0 0 1-.252-.669c0-.275.1-.512.305-.695Zm0 0v.001l.14.155-.14-.156ZM3.904 7.17c.457-.457 1.105-.672 1.91-.672h6.267c.282 0 .527.087.727.264v.001c.202.182.306.42.306.694 0 .255-.085.48-.252.668a.88.88 0 0 1-.686.295h-5.82c-.295 0-.483.077-.603.197-.118.118-.198.32-.198.653v5.357c0 .498-.1.956-.303 1.37a2.538 2.538 0 0 1-.82.986 1.99 1.99 0 0 1-.177.11c.06.034.12.07.176.11.346.242.62.573.82.987.203.414.304.872.304 1.37v5.38c0 .333.08.535.198.654.12.121.31.197.603.197h5.82c.272 0 .507.096.685.292.17.19.252.417.252.67a.909.909 0 0 1-.3.679c-.2.19-.448.28-.732.28H5.605v-.01c-.453-.022-.851-.115-1.19-.287a1.982 1.982 0 0 1-.89-.904c-.197-.396-.294-.87-.294-1.411v-5.332c0-.637-.127-1.055-.335-1.297-.2-.23-.584-.382-1.219-.397a.802.802 0 0 1-.653-.315 1.044 1.044 0 0 1-.233-.67.99.99 0 0 1 .24-.679.912.912 0 0 1 .633-.303l.01-.001c.637-.016 1.022-.167 1.222-.398.21-.241.335-.65.335-1.273V9.103c0-.818.215-1.475.673-1.933Zm18.622 7.617a2.276 2.276 0 1 0 0 4.552 2.276 2.276 0 0 0 0-4.552ZM8.939 17.063a2.276 2.276 0 1 1 4.552 0 2.276 2.276 0 0 1-4.552 0Z" clip-rule="evenodd"></path>
      </svg>
    </header>

    <h3 style="margin-bottom: 0; margin-top: 0;">👋 Welcome to Augment Code</h3>
    <div style="margin-top: 20px;margin-bottom: 20px;">It looks like <code><EMAIL></code> doesn't have an account
      yet.
    </div>

    <form method="post" action="/terms-accept?response_type=code&amp;code_challenge=kuTBvhtgp8zEXrbAsuQuV8YXmio_4OnoMQ-vKwgCXd4&amp;client_id=v&amp;state=30572bac-0a79-441e-8f02-1c0ee93f3e0f&amp;prompt=login" id="action-form">
      <input type="hidden" name="sign_up" value="true" style="">
      <input type="hidden" name="g-recaptcha-response" id="g-recaptcha-response" style="">
      <input type="hidden" name="verisoul-session-id" id="verisoul-session-id" style="">
      <input type="hidden" name="verosint-deviceid" id="verosint-deviceid" style="">
      <input type="hidden" name="client-errors" id="client-errors" style="">

      <input type="hidden" name="continue" value="continue" style="">

      <div class="u-text-sm">
        
        <label class="c-checkbox" for="terms-of-service-checkbox">
          <input id="terms-of-service-checkbox" name="terms-of-service" type="checkbox" value="accepted" onchange="updateSignupButton(this)" style="">
          <div class="c-checkbox--mark" data-testid="tos-checkbox"></div>I agree to the 
          <a target="_blank" href="https://www.augmentcode.com/terms-of-service/enterprise">terms of service</a>
           and want to start using Augment Code.

        </label>
      </div>

      <br>

      <div class="buttons">
        <button id="signup-button" class="sign-link gsi-material-button" onclick="onClick(event)" type="button">
          <div class="gsi-material-button-state"></div>
          <div class="gsi-material-button-content-wrapper">
            <span class="gsi-material-button-contents">Sign up and start coding</span>
          </div>
        </button>
      </div>

      <br>

      <div class="bottom-links u-text-sm">
        <a href="/login?response_type=code&amp;code_challenge=kuTBvhtgp8zEXrbAsuQuV8YXmio_4OnoMQ-vKwgCXd4&amp;client_id=v&amp;state=30572bac-0a79-441e-8f02-1c0ee93f3e0f&amp;prompt=login">Different Account</a>
      </div>
    </form>
  </div>
  </div>
    </div>
  <footer class="c-footer">
  <p>
    © 2025 Augment Computing, Inc.
  </p>
  <div class="c-footer__links">
    <a href="mailto:<EMAIL>">Contact Us</a>
    <a href="/privacy-policy">Privacy Policy</a>
  </div>
</footer>



<div><div class="grecaptcha-badge" data-style="bottomright" style="width: 256px; height: 60px; display: block; transition: right 0.3s; position: fixed; bottom: 14px; right: -186px; box-shadow: gray 0px 0px 5px; border-radius: 2px; overflow: hidden;"><div class="grecaptcha-logo"><iframe title="reCAPTCHA" width="256" height="60" role="presentation" name="a-hr4dff9gov2o" frameborder="0" scrolling="no" sandbox="allow-forms allow-popups allow-same-origin allow-scripts allow-top-navigation allow-modals allow-popups-to-escape-sandbox allow-storage-access-by-user-activation" src="https://www.google.com/recaptcha/enterprise/anchor?ar=1&amp;k=6LcoVhMrAAAAACg2fvNox_iH00SjOIWoewNh_PX1&amp;co=aHR0cHM6Ly9hdXRoLmF1Z21lbnRjb2RlLmNvbTo0NDM.&amp;hl=en&amp;v=DBIsSQ0s2djD_akThoRUDeHa&amp;size=invisible&amp;anchor-ms=20000&amp;execute-ms=15000&amp;cb=vms3scww0lk0"></iframe></div><div class="grecaptcha-error"></div><textarea id="g-recaptcha-response-100000" name="g-recaptcha-response" class="g-recaptcha-response" style="width: 250px; height: 40px; border: 1px solid rgb(193, 193, 193); margin: 10px 25px; padding: 0px; resize: none; display: none;"></textarea></div><iframe style="display: none;"></iframe></div></body></html>