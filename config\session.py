#!/usr/bin/env python3
"""
Session配置文件
用于管理动态session值，方便修改和维护
"""

# Session变量 - 可以随时修改此值
SESSION = ".eJxNyssKgCAQheF3mXWEEkm16k1k0rEGUqMbRPXuWat25_x8Jzj0PB46oCdoADLoeafw-2ynvS00a7YpkEceL1XV0jhBnVQobIGuFGmgSvyjn0p6RhPDVErRDnF9W26ih_sB-IImtg.aJHP9A.DOhTJklCZZrw4exvfNoELrtsNBs"

# Cookie域名
DOMAIN = "auth.augmentcode.com"

def get_session():
    """获取当前session值"""
    return SESSION

def get_domain():
    """获取当前域名"""
    return DOMAIN

def get_cookie_dict():
    """获取完整的Cookie字典"""
    return {
        "name": "session",
        "value": SESSION,
        "domain": DOMAIN,
        "path": "/",
        "httpOnly": True,
        "secure": True,
        "session": True,
        "expires": 1785922435,
        "sameSite": "lax"
    }
