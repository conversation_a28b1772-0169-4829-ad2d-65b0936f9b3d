#!/usr/bin/env python3
"""
Session配置文件
用于管理动态session值，方便修改和维护
"""

# Session变量 - 可以随时修改此值
SESSION = ".eJzdUttO4zAU_JdI9AVEG4eUtFK1u0CbgmgoJQlJXiLHdlu3vilxaFrg39fJaiWk5QtWsmX7eI48npl3K1ek5FAQoa2xLmtyYeWVhprksNbbQc7khooc8fiE_FGNQLzGc3bIQntXgBV74MFb5sfrFGy3hd-wgmO2TILkKZkt8K19i-eBH07jeMFdWog4wf62QdMMvPjTw1PEdijSsyySDoqaGzib8hQ0Knt1Bwlg-2WScuSsttiPRoG_aNIT49nuXi94egxuB4M0jAaPIaNZuNEBmIKnu40dhGmT-Q_7B3FzLIB9wHd2VQCsCprZy0i5eD6bJqeAF_bNmzV-tzDUsF2FFIhYY0sA76Se3SRVM-HMvVQ9ziPrwioJpiVBOq9LalBbrVU17vdbhS5hveFGPCQxuUSS92VbBX0EGSsg2pvuumRfmjpB_-lqm2RJT-RHSSolRUVyfVRk0gJ6iFGDzSmeHNhjGL_6d-t7_Tqan-i9PLysKHkOVke2WIaw95XppHvyzPl1BmZmfMfWlP_wNZu_jHsVkuZlMwXF54RDys5VKdeUkV6XjMn_mIae-SJXetLZ0-vyMPk2DZ8XFmmUNbav3SvHGw2HzuW1d-3ZQ9fcrCGn7JgLyNs0Ge839I2IL2eKVV5XpDRmmkIn7sfQG9nYQ3ZhD-EAO3DtInI1JF4bnRbaoQx6v6GI_6wkl7vWO-vzNw5OSKw.aJHZCQ.ElsFds2CtvheDRsU2PBnSSB17FU"

# Cookie域名
DOMAIN = "auth.augmentcode.com"

def get_session():
    """获取当前session值"""
    return SESSION

def get_domain():
    """获取当前域名"""
    return DOMAIN

def get_cookie_dict():
    """获取完整的Cookie字典"""
    return {
        "name": "session",
        "value": SESSION,
        "domain": DOMAIN,
        "path": "/",
        "httpOnly": True,
        "secure": True,
        "expires": 1785922435,
        "sameSite": "Lax"
    }
