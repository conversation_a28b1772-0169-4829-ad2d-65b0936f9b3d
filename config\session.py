#!/usr/bin/env python3
"""
Session配置文件
用于管理动态session值，方便修改和维护
"""

# Session变量 - 可以随时修改此值
SESSION = ".eJxNykEKgzAQQNG7zFqkVmOrK28SRjPKFCeRJAii3r3Blcv_eQfohbygJRuhjX6lDEYUnndtUQhagAwm3sg-ms2i10Bes0mDBHk-629TlNg3fVHjy5Q4qspUb4WJ3_RWSauowqcLTtwvH5zA9QcqqyrJ.aJE6yA.2HWpbB5EPHBNZBUBGnclSnfTOMQ"

# Cookie域名
DOMAIN = "auth.augmentcode.com"

def get_session():
    """获取当前session值"""
    return SESSION

def get_domain():
    """获取当前域名"""
    return DOMAIN

def get_cookie_dict():
    """获取完整的Cookie字典"""
    return {
        "name": "session",
        "value": SESSION,
        "domain": DOMAIN,
        "path": "/",
        "httpOnly": True,
        "secure": True,
        "session": True,
        "expires": 1785922435,
        "sameSite": "Lax"
    }
