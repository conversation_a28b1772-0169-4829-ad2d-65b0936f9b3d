#!/usr/bin/env python3
"""
Playwright单应用自动化脚本
高性能、现代化的浏览器自动化解决方案

{
 "env": {
    "ANTHROPIC_BASE_URL": "https://gaccode.com/claudecode",
    "ANTHROPIC_API_KEY": "sk-ant-oat01-8b000aea4781426871adf2a14402c040516ec5d806371e1c8991f2fea18645d8",
    "ANTHROPIC_AUTH_TOKEN": "sk-ant-oat01-8b000aea4781426871adf2a14402c040516ec5d806371e1c8991f2fea18645d8"
  }
  
}
特点：
- 单文件应用，无复杂依赖
- 基于Playwright，性能优异
- 内置反检测机制
- 智能元素定位
- 人类行为模拟
"""

import asyncio
import json
import logging
import random

import time
import yaml
from pathlib import Path
from typing import Dict, Any, Optional, List

# 添加config目录到Python路径
try:
    from config.session import get_session, get_domain, get_cookie_dict
    SESSION_CONFIG_AVAILABLE = True
except ImportError:
    SESSION_CONFIG_AVAILABLE = False
    print("⚠️ Session配置文件未找到，将使用默认Cookie配置")

try:
    from playwright.async_api import async_playwright, Browser, Page, BrowserContext
    PLAYWRIGHT_AVAILABLE = True
except ImportError:
    PLAYWRIGHT_AVAILABLE = False
    print("❌ Playwright未安装")
    print("安装命令: pip install playwright && playwright install chromium")
    exit(1)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('app.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)


class PlaywrightApp:
    """Playwright自动化应用"""
    
    def __init__(self):
        self.config = self._load_config()
        self.cookies = self._load_cookies()
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.page: Optional[Page] = None
        
        # 从配置获取目标URL
        self.target_url = self.config.get('target', {}).get('url', '')
        if not self.target_url:
            raise ValueError("配置文件中未找到目标URL")
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            config_path = Path('config/settings.yaml')
            if not config_path.exists():
                logger.error("配置文件不存在: config/settings.yaml")
                return {}
            
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
                logger.info("✓ 配置文件加载成功")
                return config
        except Exception as e:
            logger.error(f"配置文件加载失败: {e}")
            return {}
    
    def _load_cookies(self) -> List[Dict]:
        """加载Cookie文件，支持动态session"""
        try:
            # 优先使用动态session配置
            if SESSION_CONFIG_AVAILABLE:
                logger.info("使用动态session配置")
                cookie_dict = get_cookie_dict()
                cookies = [cookie_dict]
                logger.info(f"✓ 加载了动态session Cookie: {get_domain()}")
                return cookies

            # 回退到静态Cookie文件
            cookie_path = Path('config/cookies.json')
            if not cookie_path.exists():
                logger.warning("Cookie文件不存在，将跳过Cookie设置")
                return []

            with open(cookie_path, 'r', encoding='utf-8') as f:
                cookies = json.load(f)
                logger.info(f"✓ 加载了 {len(cookies)} 个静态Cookie")
                return cookies
        except Exception as e:
            logger.warning(f"Cookie加载失败: {e}")
            return []
    
    async def _setup_browser(self) -> bool:
        """设置浏览器"""
        try:
            logger.info("启动Playwright浏览器...")
            
            # 启动Playwright
            playwright = await async_playwright().start()
            
            # 浏览器启动参数
            browser_args = [
                '--no-sandbox',
                '--disable-dev-shm-usage',
                '--disable-blink-features=AutomationControlled',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor',
                '--disable-background-timer-throttling',
                '--disable-backgrounding-occluded-windows',
                '--disable-renderer-backgrounding',
                '--disable-field-trial-config',
                '--disable-back-forward-cache',
                '--disable-ipc-flooding-protection',
            ]
            
            # 启动Chromium浏览器
            self.browser = await playwright.chromium.launch(
                headless=False,  # 设为True可提升性能
                args=browser_args,
                slow_mo=random.randint(50, 150)  # 随机减慢操作，模拟人类
            )
            
            # 创建浏览器上下文
            self.context = await self.browser.new_context(
                viewport={'width': 1920, 'height': 1080},
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                java_script_enabled=True,
                ignore_https_errors=True,
                locale='en-US',
                timezone_id='America/New_York',
            )
            
            # 应用反检测脚本
            await self._apply_stealth()
            
            # 创建页面
            self.page = await self.context.new_page()
            
            # 设置超时
            self.page.set_default_timeout(15000)  # 15秒超时
            
            logger.info("✓ 浏览器设置完成")
            return True
            
        except Exception as e:
            logger.error(f"浏览器设置失败: {e}")
            return False
    
    async def _apply_stealth(self):
        """应用反检测脚本"""
        try:
            # 注入反检测脚本
            await self.context.add_init_script("""
                // 移除webdriver标识
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined,
                });
                
                // 伪造Chrome对象
                window.chrome = {
                    runtime: {},
                    loadTimes: function() {},
                    csi: function() {},
                    app: {
                        isInstalled: false,
                        InstallState: {
                            DISABLED: 'disabled',
                            INSTALLED: 'installed',
                            NOT_INSTALLED: 'not_installed'
                        },
                        RunningState: {
                            CANNOT_RUN: 'cannot_run',
                            READY_TO_RUN: 'ready_to_run',
                            RUNNING: 'running'
                        }
                    }
                };
                
                // 伪造插件
                Object.defineProperty(navigator, 'plugins', {
                    get: () => [
                        {
                            0: {type: "application/x-google-chrome-pdf", suffixes: "pdf", description: "Portable Document Format"},
                            description: "Portable Document Format",
                            filename: "internal-pdf-viewer",
                            length: 1,
                            name: "Chrome PDF Plugin"
                        }
                    ]
                });
                
                // 伪造语言
                Object.defineProperty(navigator, 'languages', {
                    get: () => ['en-US', 'en']
                });
                
                // 清理自动化痕迹
                delete window.document.$cdc_asdjflasutopfhvcZLmcfl_;
                delete window.document.$chrome_asyncScriptInfo;
                
                // 伪造权限API
                const originalQuery = window.navigator.permissions.query;
                window.navigator.permissions.query = (parameters) => (
                    parameters.name === 'notifications' ?
                        Promise.resolve({ state: Notification.permission }) :
                        originalQuery(parameters)
                );
                
                // 随机化Canvas指纹
                const getImageData = HTMLCanvasElement.prototype.getContext('2d').getImageData;
                HTMLCanvasElement.prototype.getContext('2d').getImageData = function(sx, sy, sw, sh) {
                    const imageData = getImageData.apply(this, arguments);
                    for (let i = 0; i < imageData.data.length; i += 4) {
                        imageData.data[i] += Math.floor(Math.random() * 10) - 5;
                        imageData.data[i + 1] += Math.floor(Math.random() * 10) - 5;
                        imageData.data[i + 2] += Math.floor(Math.random() * 10) - 5;
                    }
                    return imageData;
                };
                
                console.log('🛡️ 反检测脚本已应用');
            """)
            
            logger.info("✓ 反检测脚本应用成功")
            
        except Exception as e:
            logger.warning(f"反检测脚本应用失败: {e}")
    
    def _validate_cookie_format(self, cookie: Dict) -> bool:
        """验证Cookie格式"""
        required_fields = ['name', 'value']
        for field in required_fields:
            if field not in cookie:
                logger.error(f"Cookie缺少必需字段: {field}")
                return False

        # 验证domain或url字段
        if 'domain' not in cookie and 'url' not in cookie:
            logger.error("Cookie必须包含domain或url字段")
            return False

        return True

    def _get_target_domain(self) -> str:
        """获取目标域名用于Cookie验证"""
        from urllib.parse import urlparse
        parsed_url = urlparse(self.target_url)
        return f"{parsed_url.scheme}://{parsed_url.netloc}"

    async def _set_cookies(self):
        """设置Cookie - 在页面访问之前设置"""
        if not self.cookies:
            logger.info("无Cookie需要设置")
            return True

        try:
            # 验证Cookie格式
            valid_cookies = []
            for cookie in self.cookies:
                if self._validate_cookie_format(cookie):
                    valid_cookies.append(cookie)
                else:
                    logger.warning(f"跳过无效Cookie: {cookie}")

            if not valid_cookies:
                logger.warning("没有有效的Cookie可设置")
                return False

            # 设置Cookie（在访问页面之前）
            logger.info(f"准备设置 {len(valid_cookies)} 个Cookie")
            await self.context.add_cookies(valid_cookies)
            logger.info(f"✓ 成功设置了 {len(valid_cookies)} 个Cookie")

            # 验证Cookie是否设置成功（使用动态域名）
            target_domain = self._get_target_domain()
            current_cookies = await self.context.cookies(target_domain)
            logger.info(f"🔍 验证Cookie设置结果 - {target_domain} 的所有cookie:")

            if current_cookies:
                for cookie in current_cookies:
                    logger.info(f"  - {cookie['name']}: {cookie['value'][:50]}{'...' if len(cookie['value']) > 50 else ''}")
                return True
            else:
                logger.warning("⚠️ 未检测到任何cookie，设置可能失败")
                return False

        except Exception as e:
            logger.error(f"Cookie设置失败: {e}")
            return False
    
    async def _human_like_click(self, selector: str, description: str = "") -> bool:
        """人类似点击"""
        try:
            logger.info(f"查找元素: {description or selector}")
            
            # 等待元素出现
            await self.page.wait_for_selector(selector, timeout=10000)
            
            # 获取元素
            element = self.page.locator(selector).first
            
            # 滚动到元素可见
            await element.scroll_into_view_if_needed()
            
            # 模拟人类行为：短暂等待
            await asyncio.sleep(random.uniform(0.5, 1.5))
            
            # 获取元素位置
            box = await element.bounding_box()
            if not box:
                logger.error(f"无法获取元素位置: {selector}")
                return False
            
            # 计算点击位置（添加随机偏移）
            x = box['x'] + box['width'] / 2 + random.randint(-5, 5)
            y = box['y'] + box['height'] / 2 + random.randint(-5, 5)
            
            # 模拟鼠标移动
            await self.page.mouse.move(x, y)
            await asyncio.sleep(random.uniform(0.1, 0.3))
            
            # 执行点击
            await self.page.mouse.click(x, y)
            await asyncio.sleep(random.uniform(0.2, 0.5))
            
            logger.info(f"✓ 成功点击: {description or selector}")
            return True
            
        except Exception as e:
            logger.error(f"点击失败 {description or selector}: {e}")
            return False
    
    async def _save_debug_info(self, step_name: str):
        """保存调试信息"""
        try:
            # 创建debug目录
            debug_dir = Path('debug')
            debug_dir.mkdir(exist_ok=True)

            # 保存页面HTML
            content = await self.page.content()
            html_file = debug_dir / f"{step_name}_page.html"
            with open(html_file, 'w', encoding='utf-8') as f:
                f.write(content)
            logger.info(f"✓ 保存页面HTML: {html_file}")

            # 保存截图
            screenshot_file = debug_dir / f"{step_name}_screenshot.png"
            await self.page.screenshot(path=str(screenshot_file), full_page=True)
            logger.info(f"✓ 保存截图: {screenshot_file}")

            # 分析页面中的所有输入元素
            inputs = await self.page.query_selector_all('input, button, [role="checkbox"], [role="button"]')
            logger.info(f"📊 页面中发现 {len(inputs)} 个可交互元素:")

            for i, element in enumerate(inputs[:20]):  # 只显示前20个
                try:
                    tag_name = await element.evaluate('el => el.tagName')
                    element_type = await element.evaluate('el => el.type || "unknown"')
                    element_id = await element.evaluate('el => el.id || ""')
                    element_class = await element.evaluate('el => el.className || ""')
                    element_text = await element.evaluate('el => el.textContent || el.value || ""')

                    logger.info(f"  {i+1}. {tag_name}[type={element_type}] id='{element_id}' class='{element_class}' text='{element_text[:50]}'")
                except:
                    continue

        except Exception as e:
            logger.warning(f"保存调试信息失败: {e}")

    async def _smart_find_and_click(self, selectors: List[str], description: str) -> bool:
        """智能查找并点击元素"""
        # 先保存调试信息
        await self._save_debug_info(f"before_{description.replace(' ', '_')}")

        # 等待页面稳定
        await asyncio.sleep(2)

        # 尝试等待任何复选框出现
        try:
            await self.page.wait_for_selector('input, button, [role="checkbox"]', timeout=10000)
            logger.info("✓ 检测到页面中有可交互元素")
        except:
            logger.warning("⚠️ 未检测到可交互元素")

        for i, selector in enumerate(selectors):
            try:
                logger.info(f"尝试选择器 {i+1}/{len(selectors)}: {selector}")

                # 检查元素是否存在
                element_count = await self.page.locator(selector).count()
                if element_count > 0:
                    logger.info(f"✓ 找到 {element_count} 个匹配元素: {selector}")
                    if await self._human_like_click(selector, description):
                        return True
                else:
                    logger.debug(f"选择器未找到元素: {selector}")

            except Exception as e:
                logger.debug(f"选择器失败 {selector}: {e}")
                continue

        # 如果所有选择器都失败，尝试更通用的方法
        logger.warning("🔍 尝试通用查找方法...")

        # 查找包含特定文本的元素
        text_patterns = ['terms', 'Terms', 'agreement', 'Agreement', 'accept', 'Accept', 'agree', 'Agree']
        for pattern in text_patterns:
            try:
                # 查找包含文本的标签和其中的输入元素
                text_selector = f"text={pattern}"
                if await self.page.locator(text_selector).count() > 0:
                    logger.info(f"✓ 找到包含文本 '{pattern}' 的元素")

                    # 尝试点击包含该文本的标签
                    label_selector = f"label:has-text('{pattern}')"
                    if await self.page.locator(label_selector).count() > 0:
                        if await self._human_like_click(label_selector, f"包含'{pattern}'的标签"):
                            return True

                    # 尝试查找该文本附近的输入元素
                    nearby_input = f"text={pattern} >> .. >> input"
                    if await self.page.locator(nearby_input).count() > 0:
                        if await self._human_like_click(nearby_input, f"'{pattern}'附近的输入"):
                            return True

            except Exception as e:
                logger.debug(f"文本查找失败 {pattern}: {e}")
                continue

        logger.error(f"所有选择器都失败了: {description}")
        return False

    async def execute(self) -> bool:
        """执行自动化任务"""
        try:
            logger.info("🚀 开始Playwright自动化执行")

            # 1. 设置浏览器
            if not await self._setup_browser():
                return False

            # 2. 设置Cookie（在访问页面之前）
            cookie_success = await self._set_cookies()
            if not cookie_success:
                logger.warning("⚠️ Cookie设置失败，但继续执行")

            # 3. 访问目标页面
            logger.info(f"访问目标页面: {self.target_url}")
            await self.page.goto(self.target_url, wait_until='networkidle')

            # 等待页面完全加载和JavaScript执行
            logger.info("⏳ 等待页面完全加载...")
            await asyncio.sleep(3)

            # 等待任何可能的动态内容加载
            try:
                await self.page.wait_for_load_state('networkidle', timeout=10000)
                logger.info("✓ 网络空闲状态达成")
            except:
                logger.warning("⚠️ 网络空闲等待超时，继续执行")

            # 额外等待确保所有JavaScript完成执行
            await asyncio.sleep(2)

            # 4. 查找并点击复选框
            logger.info("🔍 查找复选框...")

            checkbox_selectors = [
                # 按优先级排序的选择器
                "[data-testid='tos-checkbox']",
                "[data-testid*='terms']",
                "[data-testid*='agreement']",
                "input[type='checkbox']",
                ".checkbox input",
                "label:has-text('Terms') input",
                "label:has-text('terms') input",
                "label:has-text('Agreement') input",
                "label:has-text('agreement') input",
                "label:has-text('Accept') input",
                "label:has-text('accept') input",
                "label:has-text('Agree') input",
                "label:has-text('agree') input",
                "[role='checkbox']",
                ".terms-checkbox",
                ".agreement-checkbox",
                ".tos-checkbox",
                "#terms-checkbox",
                "#agreement-checkbox",
                "#tos-checkbox",
                "input[name*='terms']",
                "input[name*='agreement']",
                "input[name*='tos']",
                "input[id*='terms']",
                "input[id*='agreement']",
                "input[id*='tos']",
                # 更通用的选择器
                "label:contains('Terms')",
                "label:contains('Agreement')",
                "label:contains('Accept')",
                "div:has-text('Terms') input",
                "div:has-text('Agreement') input",
                "span:has-text('Terms') input",
                "span:has-text('Agreement') input",
                # 最后尝试所有复选框
                "input[type='checkbox']:first-of-type",
                "input[type='checkbox']:last-of-type",
            ]

            if not await self._smart_find_and_click(checkbox_selectors, "服务条款复选框"):
                logger.error("❌ 复选框点击失败")
                return False

            # 等待状态更新
            await asyncio.sleep(1)

            # 5. 查找并点击注册按钮
            logger.info("🔍 查找注册按钮...")

            button_selectors = [
                # 按优先级排序的选择器
                "text=Sign up and start coding",
                "button:has-text('Sign up')",
                "button:has-text('Register')",
                "[role='button']:has-text('Sign up')",
                "input[type='submit']",
                ".signup-button",
                "#signup-button",
                "button[name*='signup']",
                "button[id*='signup']",
                "a:has-text('Sign up')",
            ]

            if not await self._smart_find_and_click(button_selectors, "注册按钮"):
                logger.error("❌ 注册按钮点击失败")
                return False

            # 6. 等待结果
            logger.info("⏳ 等待操作结果...")
            await asyncio.sleep(3)

            # 7. 检查结果
            try:
                # 获取页面内容
                content = await self.page.content()
                url = self.page.url
                title = await self.page.title()

                logger.info(f"当前URL: {url}")
                logger.info(f"页面标题: {title}")

                # 检查是否被拒绝
                if any(keyword in content.lower() for keyword in ['rejected', 'denied', 'error', 'failed']):
                    logger.warning("⚠️ 检测到可能的拒绝信号")
                    return False

                # 检查是否成功
                if any(keyword in content.lower() for keyword in ['success', 'welcome', 'dashboard', 'profile']):
                    logger.info("✅ 检测到成功信号")
                    return True

                # 如果没有明确的成功/失败信号，认为操作完成
                logger.info("✅ 自动化操作完成")
                return True

            except Exception as e:
                logger.warning(f"结果检查失败: {e}")
                # 即使检查失败，也认为操作完成
                return True

        except Exception as e:
            logger.error(f"自动化执行失败: {e}")
            return False

        finally:
            # 保持浏览器打开以便调试
            await self.cleanup(keep_browser_open=True)

    async def cleanup(self, keep_browser_open=True):
        """清理资源"""
        try:
            if keep_browser_open:
                logger.info("🔍 保持浏览器打开以便调试...")
                logger.info("💡 提示：手动关闭浏览器窗口或按Ctrl+C结束程序")

                # 等待用户手动关闭或中断
                try:
                    while True:
                        await asyncio.sleep(1)
                        # 检查浏览器是否还在运行
                        if self.browser and not self.browser.is_connected():
                            break
                except KeyboardInterrupt:
                    logger.info("⚠️ 用户中断，开始清理资源...")

            # 正常清理资源
            if self.page:
                await self.page.close()
            if self.context:
                await self.context.close()
            if self.browser:
                await self.browser.close()
            logger.info("✓ 资源清理完成")
        except Exception as e:
            logger.debug(f"资源清理失败: {e}")


async def main():
    """主函数"""
    print("🎭 Playwright单应用自动化脚本")
    print("=" * 50)

    start_time = time.time()

    try:
        # 创建应用实例
        app = PlaywrightApp()

        # 显示配置信息
        print(f"目标URL: {app.target_url}")
        print(f"Cookie数量: {len(app.cookies)}")

        # 执行自动化
        success = await app.execute()

        # 计算执行时间
        execution_time = time.time() - start_time

        if success:
            print(f"✅ 任务执行成功! 耗时: {execution_time:.2f}秒")
            return True
        else:
            print(f"❌ 任务执行失败! 耗时: {execution_time:.2f}秒")
            return False

    except KeyboardInterrupt:
        print("\n⚠️ 用户中断执行")
        return False
    except Exception as e:
        execution_time = time.time() - start_time
        print(f"❌ 执行出错: {e}")
        logger.error(f"执行出错: {e}")
        return False


def install_playwright():
    """安装Playwright"""
    import subprocess
    import sys

    print("🔧 检测到Playwright未安装，开始安装...")

    try:
        # 安装playwright
        subprocess.check_call([sys.executable, "-m", "pip", "install", "playwright"])

        # 安装浏览器
        subprocess.check_call([sys.executable, "-m", "playwright", "install", "chromium"])

        print("✅ Playwright安装完成!")
        print("请重新运行脚本: python app.py")

    except subprocess.CalledProcessError as e:
        print(f"❌ Playwright安装失败: {e}")
        print("请手动安装:")
        print("pip install playwright")
        print("playwright install chromium")


if __name__ == "__main__":
    if not PLAYWRIGHT_AVAILABLE:
        install_playwright()
    else:
        success = asyncio.run(main())
        exit(0 if success else 1)
